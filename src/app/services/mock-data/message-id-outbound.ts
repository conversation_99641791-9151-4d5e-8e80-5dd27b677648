export const MockMessageIdOutbound: string[] = [
  "FA460",
  "SWSDO",
  "FA486",
  "SW855",
  "SWPO<PERSON>",
  "SWADM",
  "SWRYB",
  "FA420",
  "FA810",
  "FA429",
  "FA427",
  "<PERSON>W<PERSON><PERSON>",
  "SW429",
  "BLA<PERSON><PERSON>",
  "FA440",
  "SW838",
  "FA433",
  "FA322",
  "FA943",
  "FA990",
  "SW315",
  "AR660",
  "SW46D",
  "SWETA",
  "SWRWR",
  "FA419",
  "FA161",
  "FA998",
  "FAACO",
  "SWOSW",
  "FA864",
  "FA350",
  "FA820",
  "COT5X00",
  "RA3R",
  "SW301",
  "EQADV",
  "SW219",
  "SW680",
  "BLANK",
  "SW928",
  "SW127",
  "SW810",
  "SW440",
  "SWSH<PERSON>",
  "SWADV",
  "SW997",
  "FA404",
  "SW427",
  "SW431",
  "SW475",
  "EVENT",
  "SWRMR",
  "FA434",
  "SW943",
  "FA928",
  "FA204",
  "EMBARGO",
  "SW926",
  "SW404",
  "SW463",
  "SWINV",
  "SWRAW",
  "FA423",
  "SWREM",
  "SW410",
  "SWRUI",
  "SW418",
  "FA421",
  "SW417",
  "SW426",
  "FA824",
  "SWSAM",
  "FA431",
  "SW990",
  "FA410",
  "SW210",
  "UMLRC",
  "SWBOL",
  "FA997",
  "SWEIG",
  "SW419",
  "SW421",
  "SW520",
  "SW420",
  "FA309",
  "FA426",
  "FA301",
  "GIS404",
  "EVENTS",
  "RA2VE",
  "FA214",
  "SW434",
  "SW924",
  "SW858",
  "SWTRS",
  "SW425",
  "SWEAI",
  "SW451",
  "SWEVT",
  "SW824",
  "SW423",
  "SWTEM",
  "SW820S",
  "SWACO",
  "SW998",
  "FA418",
  "SW309",
  "SW350",
  "FA858",
  "FA315",
  "SW437",
  "SWSEC",
  "JTRAN10",
  "SW610",
  "SW660",
  "SWCRI",
  "SW820",
  "SW322",
  "FA463",
  "SW550",
  "SW996",
  "SW433",
  "SW214",
  "PERMITNT",
  "SWAYB",
  "FA490",
  "SWCHI",
  "SW161",
  "SWTOA",
  "SWPUR",
  "FA425",
  "SW850",
  "FA417",
  "SW8X4",
  "SWINV02",
  "SW204",
  "FA924"
];
