export const MockApplicationSenderInbound: string[] = [
  "SBD",
  "CSXI",
  "CSXINC",
  "CSX1",
  "36964",
  "VISTA",
  "4126824700",
  "7033875639",
  "131461733",
  "113088082",
  "INDRAILUSP",
  "STANRAIL",
  "3015152200",
  "NOPRINT",
  "AFGX",
  "151335742",
  "077316412PA",
  "BBDW",
  "BBQC",
  "006308407",
  "EMKWRINC",
  "EMCLEVE",
  "XOMLUBNADS",
  "CN",
  "CFE",
  "59-2869009",
  "MHWA",
  "NCIR",
  "POPBALCSX",
  "TDSIRA<PERSON>",
  "8004171844AD",
  "ARDAGHGNAT",
  "CXAN",
  "POHC",
  "EXPRAIL",
  "RMXXPOC1",
  "009056904T",
  "CSXT",
  "36460",
  "340961012P",
  "003396959",
  "FAX",
  "138183702",
  "614733525",
  "024077364",
  "006150528",
  "STRS",
  "DUWS",
  "2059561122",
  "WAB360361",
  "5403875639",
  "047583851ANX",
  "DECRT",
  "WAB300302",
  "EMJAXON",
  "ODXPEDX",
  "GEHH",
  "9134843220",
  "PET3",
  "UP",
  "MQT",
  "MNBR",
  "6125A8A",
  "007941321",
  "&CIF",
  "UNIMINBDS",
  "ADM",
  "ST",
  "GSCO",
  "CPRST",
  "FRR",
  "ETSLAX",
  "ICLFCA",
  "ISUPPLIER",
  "SUPX",
  "8003339826",
  "927489WATR",
  "GSIG",
  "107627952",
  "USWS",
  "005129127",
  "SMITHPACKAGE",
  "EMDOME",
  "005003335",
  "EASTPENN",
  "PGLI",
  "BNSF",
  "JAMR",
  "BPIE",
  "RRWS",
  "CMACGM",
  "RRWE",
  "TRANSFLO",
  "54-1751248",
  "RAILINCEOTAPI",
  "29546",
  "PAS",
  "COPAY",
  "TSM",
  "00-132-5166",
  "108407628",
  "9728707000",
  "049295223",
  "WABSCT",
  "8043586776",
  "045905072",
  "181163007",
  "WW",
  "INRD",
  "H9T4A",
  "CSO",
  "AGR",
  "DELMONTE",
  "&CSM",
  "9725437900",
  "GOODYEAR",
  "CPRSP",
  "POET",
  "ISSV",
  "&MRK",
  "VTR",
  "RLX0216",
  "ADMCLM",
  "CSXTCRSP",
  "CX",
  "MONSANTO",
  "5403456956",
  "7732541110",
  "BBHJ",
  "301407686",
  "624228854",
  "TESSCO",
  "EMIFS",
  "182755462",
  "BBEH",
  "RICR",
  "GITM",
  "WE",
  "USCT",
  "592869009",
  "CSXIHOST",
  "CSXCLP",
  "ITELPEGAS",
  "DOWCOR",
  "RRDC",
  "TYBR",
  "29749",
  "NOTSTCS",
  "GRAINCRAFT",
  "MPLI",
  "TRNHLI",
  "BOCT",
  "005016589",
  "000363",
  "WABTEP",
  "APSGOGO",
  "NS",
  "FGLK",
  "SAPT",
  "GLOVIS",
  "RZ1UMXU",
  "STCS",
  "CASS151700T",
  "RICRJSON",
  "CSXINTMDL",
  "006173082",
  "NYAB",
  "RJCW",
  "62002",
  "BBYB",
  "MIBAAS2",
  "HYUM",
  "CSXR",
  "USCP",
  "FEC",
  "BB",
  "SCXF",
  "GMPTSTECH",
  "BAIL",
  "NPB",
  "COYN",
  "XXQI",
  "ITELIPG",
  "TRADELENS",
  "NUCOR322",
  "ISSP",
  "ESPN",
  "SLRS",
  "19-4282745",
  "PLASSER",
  "VWASN",
  "CXSO",
  "HPTD",
  "065906430",
  "CSX",
  "RAMESH",
  "STEPRO",
  "CLAFIL",
  "195590237",
  "SAFETPBX",
  "016076283",
  "RAILHEAD",
  "EMRLMILLER",
  "EMRRTOOL",
  "009056904",
  "6106826361",
  "CS",
  "CPRS",
  "ACIA8A",
  "CSXUMAX",
  "005130182",
  "NUCB",
  "CSXO",
  "65906430",
  "FINP",
  "CSRI",
  "TEST"
];
