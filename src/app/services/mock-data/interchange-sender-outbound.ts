export const MockInterchangeSenderOutbound: string[] = [
  "CSXIN<PERSON>",
  "SBD CSR",
  "EXPRAIL",
  "CSXI",
  "SBD",
  "CSX1",
  "TDSIRAMP",
  "J26<PERSON>",
  "SBD SBD",
  "CN",
  "FEC ECDX",
  "POPBALCSX",
  "UPT",
  "36964",
  "CXAN",
  "RMXXPOHC",
  "RMXXFRR",
  "CSXT",
  "CSXTPROD",
  "36460",
  "SBD TDCC",
  "CPRP",
  "UP",
  "RMXXNCIR",
  "NUCBECDX",
  "CNINC",
  "TL20",
  "CSXTITBSYSTEM",
  "SUBARU",
  "BNSFINC",
  "BNSF",
  "RMXXSAPT",
  "RMXXFGLK",
  "BPIE",
  "CMMXMHWA",
  "RRWS",
  "RRWE",
  "ITEL",
  "RAILINC",
  "TRANSFLO",
  "E4LSW",
  "RMXXPAS",
  "SHIPCSX",
  "RMXXMQT",
  "R<PERSON>XGI<PERSON>",
  "TSM",
  "H9T4A",
  "<PERSON><PERSON><PERSON>CS<PERSON>",
  "CN  ECDX",
  "AGR",
  "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  "HEXION",
  "CPRSP",
  "ISSV",
  "RLX0216",
  "CSXTTEST",
  "AMSTYCSX",
  "SBD TSM",
  "WE",
  "USCT",
  "CSXTINC",
  "CSXIHOST",
  "CSXCLP",
  "29749",
  "GRAINCRAFT",
  "CSXTCSLI",
  "TRNHLI",
  "CSXTSAA",
  "BOCT",
  "CSX2",
  "NS",
  "RMXXINRD",
  "CMMXBB",
  "RZ1UMXU",
  "H9T4",
  "CSXTTDCC",
  "USCP",
  "CSXTCSXT",
  "RMXXSCXF",
  "FEC",
  "AS2CSX",
  "ISSP",
  "TFKB",
  "INOS",
  "PTCX",
  "CSXR",
  "CASSRAIL",
  "RRWSVTR",
  "VWASN",
  "GEAP",
  "065906430",
  "RMXXMNBR",
  "RMXXCFE",
  "CANC",
  "CSX",
  "208334425",
  "CSXO",
  "PCTX",
  "CSXUMAX"
];
