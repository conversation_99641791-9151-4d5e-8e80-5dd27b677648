<div class="page-container">
  <!-- Sidebar -->
  <div class="sidebar">
    <div class="sidebar-content">
      <form [formGroup]="searchForm">
        <div class="sidebar-section">
          <h3 class="csx-text-headline">Additional Search Criteria</h3>

          <!-- Inbound Section -->
          <div class="filter-section">
            <h3 class="csx-text-title-sm">Inbound</h3>
            <div formGroupName="sidebarInboundFilters">
              <div class="checkbox-item" *ngFor="let option of sidebarInboundOptions">
                <p-checkbox
                  [formControlName]="option.value"
                  [value]="true"
                  [inputId]="'inbound_' + option.value"
                >
                </p-checkbox>
                <label [for]="'inbound_' + option.value" class="csx-text-label-md">{{
                  option.label
                }}</label>
              </div>
            </div>
          </div>

          <!-- Outbound Section -->
          <div class="filter-section">
            <h3 class="csx-text-title-sm">Outbound</h3>
            <div formGroupName="sidebarOutboundFilters">
              <div class="checkbox-item" *ngFor="let option of sidebarOutboundOptions">
                <p-checkbox
                  [formControlName]="option.value"
                  [value]="true"
                  [inputId]="'outbound_' + option.value"
                >
                </p-checkbox>
                <label [for]="'outbound_' + option.value" class="csx-text-label-md">{{
                  option.label
                }}</label>
              </div>
            </div>
          </div>

          <!-- Common EDI Attributes Section -->
          <div class="filter-section">
            <h3 class="csx-text-title-sm">Common EDI Attributes</h3>
            <div formGroupName="sidebarEdiAttributesFilters">
              <div class="checkbox-item" *ngFor="let option of sidebarEdiAttributesOptions">
                <p-checkbox
                  [formControlName]="option.value"
                  [value]="true"
                  [inputId]="'edi_' + option.value"
                >
                </p-checkbox>
                <label [for]="'edi_' + option.value" class="csx-text-label-md">{{
                  option.label
                }}</label>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Main Content -->
  <div class="content">
    <form [formGroup]="searchForm">
      <div class="select-btn-container">
        <p-selectbutton
          formControlName="searchType"
          [options]="searchTypeOptions"
          optionLabel="label"
          optionValue="value"
        />
      </div>

      <!-- Inbound/Outbound -->
      <div class="select-date-container">
        <p-select
          formControlName="direction"
          [options]="directionOptions"
          optionLabel="label"
          optionValue="value"
          placeholder="Select Direction"
        >
        </p-select>

        <!-- Date Range -->
        <div class="date-range-container">
          <div class="date-picker-wrapper">
            <p-calendar
              formControlName="startDate"
              [showTime]="true"
              [showSeconds]="false"
              [showIcon]="true"
              [hourFormat]="12"
              dateFormat="mm/dd/y"
              placeholder="Select Start Date"
              [style]="{ width: '100%' }"
              [class.ng-invalid]="hasDateRangeError && searchForm.touched"
            ></p-calendar>
          </div>
          <span>to</span>
          <div class="date-picker-wrapper">
            <p-calendar
              formControlName="endDate"
              [showTime]="true"
              [showSeconds]="false"
              [showIcon]="true"
              [hourFormat]="12"
              dateFormat="mm/dd/y"
              placeholder="Select End Date"
              [style]="{ width: '100%' }"
              [class.ng-invalid]="hasDateRangeError && searchForm.touched"
            ></p-calendar>
          </div>
        </div>
      </div>

      <!-- Inbound/Outbound Card -->
      <p-card styleClass="mb-4">
        <div class="card-content">
          <!-- Inbound Section -->
          <div class="section">
            <div class="section-title-info">
              <h3 class="csx-text-title-sm">Inbound</h3>
              <div class="search-tips">
                <span class="csx-text-label-md">Search Tips</span>
                <i
                  class="pi pi-info-circle"
                  pTooltip="Use Inbound and Outbound, Common EDI Attributes, Equipment Number, or combine them to narrow your search results."
                  tooltipPosition="right"
                ></i>
              </div>
            </div>
            <div class="grid grid-cols-2" formArrayName="inboundFields">
              <div
                *ngFor="let field of inboundFieldsFormArray.controls; let i = index"
                [formGroupName]="i"
              >
                <span class="p-input-icon-right w-full">
                  <p-iconfield>
                    <input
                      type="text"
                      pInputText
                      [placeholder]="field.get('placeholder')?.value"
                      class="w-full"
                      formControlName="value"
                    />
                    <p-inputicon styleClass="pi pi-search" />
                  </p-iconfield>
                </span>
              </div>

              <!-- Dynamic inputs for sidebar inbound filters -->
              <div formGroupName="dynamicInboundInputs">
                <div
                  *ngFor="let option of sidebarInboundOptions"
                >
                  <span
                    *ngIf="isInboundFilterSelected(option.value)"
                    class="p-input-icon-right w-full"
                  >
                    <p-iconfield>
                      <input
                        type="text"
                        pInputText
                        [placeholder]="option.label"
                        class="w-full"
                        [formControlName]="option.value"
                      />
                      <p-inputicon styleClass="pi pi-search" />
                    </p-iconfield>
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Outbound Section -->
          <div class="section">
            <h3 class="csx-text-title-sm">Outbound</h3>
            <div class="grid grid-cols-2" formArrayName="outboundFields">
              <div
                *ngFor="let field of outboundFieldsFormArray.controls; let i = index"
                [formGroupName]="i"
              >
                <span class="p-input-icon-right w-full">
                  <p-iconfield>
                    <input
                      type="text"
                      pInputText
                      [placeholder]="field.get('placeholder')?.value"
                      class="w-full"
                      formControlName="value"
                    />
                    <p-inputicon styleClass="pi pi-search" />
                  </p-iconfield>
                </span>
              </div>

              <!-- Dynamic inputs for sidebar outbound filters -->
              <div formGroupName="dynamicOutboundInputs">
                <div
                  *ngFor="let option of sidebarOutboundOptions"
                >
                  <span
                    *ngIf="isOutboundFilterSelected(option.value)"
                    class="p-input-icon-right w-full"
                  >
                    <p-iconfield>
                      <input
                        type="text"
                        pInputText
                        [placeholder]="option.label"
                        class="w-full"
                        [formControlName]="option.value"
                      />
                      <p-inputicon styleClass="pi pi-search" />
                    </p-iconfield>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </p-card>

      <!-- Common Card -->
      <p-card styleClass="mb-4">
        <div class="card-content">
          <h3 class="csx-text-title-sm">Common</h3>
          <div class="grid grid-cols-2">
            <p-select
              formControlName="transactionView"
              [options]="transactionViewOptions"
              optionLabel="label"
              optionValue="value"
              placeholder="Select Transaction View"
            ></p-select>
            <p-select
              [options]="[]"
              placeholder="Select.."
              formControlName="transactionAttributes"
            ></p-select>
          </div>

          <!-- Dynamic inputs for sidebar EDI attributes filters -->
          <div formGroupName="dynamicEdiAttributesInputs" class="mt-4">
            <h4 class="csx-text-title-sm mb-3">Common EDI Attributes</h4>
            <div class="grid grid-cols-2">
              <div
                *ngFor="let option of sidebarEdiAttributesOptions"
                [style.display]="isEdiAttributeFilterSelected(option.value) ? 'block' : 'none'"
              >
                <span class="p-input-icon-right w-full">
                  <p-iconfield>
                    <input
                      type="text"
                      pInputText
                      [placeholder]="option.label"
                      class="w-full"
                      [formControlName]="option.value"
                    />
                    <p-inputicon styleClass="pi pi-search" />
                  </p-iconfield>
                </span>
              </div>
            </div>
          </div>
        </div>
      </p-card>

      <!-- Equipment Search Card -->
      <p-card styleClass="mb-4">
        <div class="card-content">
          <h3 class="csx-text-title-sm">Search By</h3>
          <div class="grid grid-cols-2">
            <div>
              <p-select
                formControlName="equipmentSearchType"
                [options]="[{ label: 'Equipment ID', value: 'equipment' }]"
                styleClass="w-full"
              ></p-select>
            </div>
            <div>
              <input
                type="text"
                pInputText
                placeholder="Enter ID (Max 5)"
                class="w-full"
                formControlName="equipmentIds"
              />
              <small class="helper-text"
                >Enter multiple IDs separated by a comma (,). Max (5)</small
              >
            </div>
          </div>
        </div>
      </p-card>

      <!-- Date Range Validation Error -->
      <div *ngIf="hasDateRangeError && searchForm.touched" class="date-range-error">
        <small class="error-message">{{ dateRangeErrorMessage }}</small>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <p-button label="Reset" styleClass="p-button-outlined" (onClick)="onReset()"></p-button>
        <p-button label="Search" (onClick)="onSearch()" [loading]="isLoading"></p-button>
      </div>
    </form>
  </div>
</div>
