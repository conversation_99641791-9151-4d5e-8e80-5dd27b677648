:host {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  background: var(--p-neutral-50) !important;
}

.page-container {
  display: flex;
  width: 100%;
  height: 100%;
  min-height: calc(100vh - 120px); // Adjust based on header height
}

.sidebar {
  width: 350px;
  background-color: var(--p-neutral-0);
  border-right: 1px solid var(--p-neutral-100);
  overflow: hidden;
  display: flex;
  flex-direction: column;

  &-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
  }

  &-section {
    margin-bottom: 1.5rem;

    h3 {
      margin-bottom: 1rem;
      font-weight: 600;
    }
  }

  .filter-section {
    margin-bottom: 1.5rem;

    h4 {
      margin-bottom: 0.75rem;
      font-weight: 500;
    }
  }

  .checkbox-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;

    label {
      margin-left: 0.5rem;
      cursor: pointer;
    }
  }
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 1rem;
  position: relative;
  background: var(--p-neutral-50) !important;
  padding: 1rem;
}

.p-tablist-tab-list,
.p-tabpanels,
.p-tabpanel {
  background: var(--p-neutral-0) !important;
}

.select-btn-container {
  text-align: center;
  margin-bottom: 1rem;
}

.select-date-container {
  display: flex;
  width: 100%;
  max-width: 700px;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  gap: 1rem;
}

.date-range-container {
  display: flex;
  width: 100%;
  align-items: center;
  gap: 0.5rem;

  .date-picker-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;

    p-calendar {
      width: 100%;

      ::ng-deep {
        .p-calendar {
          width: 100%;
        }

        .p-inputtext {
          width: 100%;
        }
      }
    }
  }
}

.card-content {
  width: 100%;

  h3 {
    margin-bottom: 1rem;
  }
}

.section {
  margin-bottom: 2rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-tips {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-bottom: 1rem;

  span {
    display: inline-block;
  }

  i {
    font-size: 1rem;
  }
}

.field-label {
  display: block;
  margin-bottom: 0.5rem;
  width: 100%;
}

.helper-text {
  display: block;
  margin-top: 0.25rem;
  color: #718096;
  font-size: 0.75rem;
}

.advanced-search {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-top: 0.75rem;
  color: #0058f0;
  cursor: pointer;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

// Date range validation styles
.date-range-error {
  width: 100%;
  margin: 0.5rem 0 1rem;
  text-align: right;
}

.error-message {
  color: var(--p-red-500);
  font-size: 0.875rem;
  font-weight: 500;
}

// Style for invalid date inputs
::ng-deep {
  .ng-invalid.p-calendar .p-inputtext {
    border-color: var(--p-red-500) !important;
    box-shadow: 0 0 0 1px var(--p-red-500) !important;
  }
}
